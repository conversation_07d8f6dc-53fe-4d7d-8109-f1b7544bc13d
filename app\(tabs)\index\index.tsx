import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  Image,
  Dimensions 
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ShieldCheck, Award, BookOpen, Search, FileText, Users, Clock, CircleCheck as CheckCircle, ArrowRight, Target, Phone, Mail, Zap, TrendingUp, Scan, <PERSON>an as ScanIcon, ScanLine, Shield } from 'lucide-react-native';
import { router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';

const { width } = Dimensions.get('window');

const mainServices = [
  {
    id: 1,
    title: 'Certification Halal',
    description: 'Certification complète de vos produits selon les standards islamiques les plus stricts',
    icon: ShieldCheck,
    color: '#059669',
    gradient: ['#059669', '#10B981'],
    features: [
      'Audit complet des ingrédients',
      'Inspection des processus de production',
      'Certification officielle reconnue',
      'Suivi et renouvellement'
    ],
    image: 'https://images.pexels.com/photos/4110104/pexels-photo-4110104.jpeg',
  },
  {
    id: 2,
    title: 'Audit de Conformité',
    description: 'Vérification approfondie de la conformité halal de votre chaîne de production',
    icon: CheckCircle,
    color: '#3B82F6',
    gradient: ['#3B82F6', '#60A5FA'],
    features: [
      'Analyse des fournisseurs',
      'Contrôle des équipements',
      'Formation du personnel',
      'Rapport détaillé'
    ],
    image: 'https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg',
  },
  {
    id: 3,
    title: 'Formation Halal',
    description: 'Formation complète de vos équipes aux exigences et bonnes pratiques halal',
    icon: BookOpen,
    color: '#8B5CF6',
    gradient: ['#8B5CF6', '#A78BFA'],
    features: [
      'Modules théoriques et pratiques',
      'Certification du personnel',
      'Support pédagogique',
      'Suivi post-formation'
    ],
    image: 'https://images.pexels.com/photos/3184360/pexels-photo-3184360.jpeg',
  },
  {
    id: 4,
    title: 'Conseil Stratégique',
    description: 'Accompagnement personnalisé pour développer votre stratégie halal',
    icon: Target,
    color: '#F59E0B',
    gradient: ['#F59E0B', '#FBBF24'],
    features: [
      'Analyse de marché',
      'Stratégie de positionnement',
      'Plan de développement',
      'Accompagnement continu'
    ],
    image: 'https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg',
  }
];

const featuredProducts = [
  {
    id: 1,
    num: 'G2H157C11',
    brand: 'Fabrication',
    image: 'https://certitracehalal786.fr/public/upload/fabrication/1719437599.jpeg',
    
  },
  {
    id: 2,
    num: '9124000683',
    brand: 'Sacrifice',
    image: 'https://certitracehalal786.fr/public/upload/sacrifice/1719456973.jpg',
  },
  {
    id: 3,
    num: 'G2H226B11',
    brand: 'Fabrication',
    image: 'https://certitracehalal786.fr/public/upload/fabrication/1728572301.jpeg',
  },
  {
    id: 4,
    num: '0424118300',
    brand: 'Decoupe',
    image: 'https://certitracehalal786.fr/public/upload/decoupe/1729796803.jpeg',
  },
];

const certificationBodies = [
  {
    name: 'AVS Halal',
    logo: '../../assets/22.jpg',
    description: 'Association de Vérification et de Surveillance Halal'
  },
  
];
export default function ServicesScreen() {
  const handleServicePress = (serviceId: number) => {
    router.push(`/services/${serviceId}`);
  };
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Image source={require('../../../assets/cert.jpg')} style={styles.logo} />
            
          </View>
          <View style={styles.headerStats}>
            <View style={styles.statItem}>
              <TrendingUp size={16} color="#10B981" />
              <Text style={styles.statText}>+2.5k</Text>
            </View>
            <View style={styles.statItem}>
              <Users size={16} color="#10B981" />
              <Text style={styles.statText}>Certifiés</Text>
            </View>
          </View>
        </View>

        {/* Hero Section */}
        <LinearGradient
          colors={['#F0FDF4', '#ECFDF5']}
          style={styles.heroSection}>
          <View style={styles.heroContent}>
            <Text style={styles.heroTitle}>
              CertiTRACE HALAL 786
            </Text>
            <Text style={styles.heroSubtitle}>
               Votre Chemin Transparent vers le Halal Authentique.
            </Text>
            <View style={styles.heroFeatures}>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.heroFeature}>
                <Shield size={16} color="#10B981" />
                <Text style={styles.heroFeatureText}>Certification Authentique</Text>
              </View>
              <View style={styles.heroFeature}>
                <CheckCircle size={16} color="#10B981" />
                <Text style={styles.heroFeatureText}>Traçabilité Complète</Text>
              </View>
              </ScrollView>
            </View>
          </View>
        </LinearGradient>

        {/* Featured Products */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recommandés</Text>
            <TouchableOpacity style={styles.seeAllButton} onPress={() => router.push(`/certifications`)}>
              <Text style={styles.seeAllText}>Voir tout</Text>
              <ArrowRight size={14} color="#667eea" />
            </TouchableOpacity>
          </View>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {featuredProducts.map((product) => (
              <TouchableOpacity key={product.id} style={styles.productCard} onPress={() => router.push(`/certifications?search=${product.num}`)}>
                <Image source={{ uri: product.image }} style={styles.productImage} />
                <LinearGradient
                  colors={['transparent', 'rgba(0,0,0,0.7)']}
                  style={styles.productOverlay}
                />
                <View style={styles.productInfo}>
                  <Text style={styles.productName}>{product.num}</Text>
                  <Text style={styles.productBrand}>{product.brand}</Text>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>


        {/* Additional scan & rechaerche */}
        <View style={styles.section}>
          
          
          <View style={styles.additionalServices}>
            <TouchableOpacity style={styles.additionalServiceCard} onPress={() => router.push('/certifications')}>
              <Search size={32} color="#10B981" />
              <Text style={styles.additionalServiceTitle}>Recherche</Text>
              
            </TouchableOpacity>

            <TouchableOpacity style={styles.additionalServiceCard} onPress={() => router.push('/scan')}>
              <ScanLine size={32} color="#3B82F6" />
              <Text style={styles.additionalServiceTitle}>Scan</Text>
            </TouchableOpacity>

            

          </View>
        </View>

        {/* Main Services */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Nos Services</Text>
          </View>
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.servicesScroll}>
          {mainServices.map((service) => (
            <TouchableOpacity 
                
                key={service.id}
                onPress={() => handleServicePress(service.id)}
              >
              
                 <View style={styles.serviceCard}>
                 <Image
                   source={{ uri:  service.image }}
                   style={styles.serviceImage}
                 />
                 <View style={styles.serviceContent}>
                   <Text style={styles.serviceTitle}>{service.title}</Text>
                   <Text style={styles.serviceDescription}>
                   {service.description}
                   </Text>
                   
                 </View>
               </View>
              
            </TouchableOpacity>
          ))}
            
          </ScrollView>
        </View>

        {/* Certification Bodies */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Organismes de Certification</Text>
          <Text style={styles.sectionSubtitle}>
            Nous travaillons avec les organismes de certification les plus reconnus
          </Text>
          
          <View style={styles.certificationBodies}>
            {certificationBodies.map((body, index) => (
              <View key={index} style={styles.certificationCard}>
                <Image source={require('../../../assets/images/22.jpg')} style={styles.certificationLogo} />
                
              </View>
            ))}
          </View>
        </View>

        

        {/* CTA Section */}
        <View style={styles.ctaSection}>
          <LinearGradient
            colors={['#059669', '#10B981']}
            style={styles.ctaCard}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}>
            <Zap size={48} color="#FFFFFF" strokeWidth={2} />
            <Text style={styles.ctaTitle}>Prêt à commencer ?</Text>
            <Text style={styles.ctaSubtitle}>
              Contactez nos experts pour discuter de votre projet et obtenir un devis personnalisé
            </Text>
            
            <View style={styles.ctaButtons}>
              <TouchableOpacity style={styles.ctaPrimaryButton}>
                <Text style={styles.ctaPrimaryButtonText} onPress={() => router.push('https://certitracehalal786.fr/login')}>Enregister mon entreprise</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.ctaSecondaryButton}>
                <Phone size={20} color="#FFFFFF" strokeWidth={2} />
                <Text style={styles.ctaSecondaryButtonText} onPress={() => router.push('whatsapp://send?phone=+33 (0)148304863')}>Nous appeler</Text>
              </TouchableOpacity>
            </View>
            
            <View style={styles.contactInfo}>
              <View style={styles.contactItem}>
                <Mail size={16} color="#FFFFFF" strokeWidth={2} />
                <Text style={styles.contactText}><EMAIL></Text>
              </View>
              <View style={styles.contactItem}>
                <Phone size={16} color="#FFFFFF" strokeWidth={2} />
                <Text style={styles.contactText}>+33 (0)148304863</Text>
              </View>
            </View>
          </LinearGradient>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  
  headerTitle: {
    fontFamily: 'Inter-Bold',
    fontSize: 28,
    color: '#111827',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontFamily: 'Inter-Regular',
    fontSize: 16,
    color: '#6B7280',
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 20,
    color: '#111827',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 20,
  },
  mainServiceCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  serviceMainImage: {
    width: '100%',
    height: 160,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  serviceMainContent: {
    padding: 20,
  },
  serviceMainHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 12,
  },
  serviceMainTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
    color: '#111827',
  },
  serviceMainDescription: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 16,
  },
  serviceFeatures: {
    gap: 8,
    marginBottom: 16,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  featureText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    color: '#374151',
  },
  serviceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    backgroundColor: '#F0FDF4',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  serviceButtonText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
    color: '#10B981',
  },
  processContainer: {
    gap: 16,
  },
  processStep: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 16,
  },
  stepNumber: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  stepNumberText: {
    fontFamily: 'Inter-Bold',
    fontSize: 16,
  },
  stepContent: {
    flex: 1,
  },
  stepTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    color: '#111827',
    marginBottom: 4,
  },
  stepDescription: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  additionalServices: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  additionalServiceCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    width: (width - 64) / 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 4,
  },
  additionalServiceTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 15,
    color: '#111827',
    marginTop: 16,
    textAlign: 'center',
    letterSpacing: -0.2,
  },
  additionalServiceDescription: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 16,
  },
 ctaSection: {
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  ctaCard: {
    borderRadius: 24,
    padding: 32,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.3,
    shadowRadius: 24,
    elevation: 12,
  },
  ctaTitle: {
    fontSize: 24,
    color: '#FFFFFF',
    fontFamily: 'Inter-Bold',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 12,
    letterSpacing: -0.5,
  },
  ctaSubtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    opacity: 0.9,
    marginBottom: 32,
    lineHeight: 24,
  },
  ctaButtons: {
    width: '100%',
    gap: 16,
    marginBottom: 32,
  },
  certificationBodies: {
    paddingHorizontal: 20,
  },
  certificationCard: {
    backgroundColor: '#FFFFFF',
    padding: 24,
    borderRadius: 20,
    marginBottom: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.06,
    shadowRadius: 16,
    elevation: 8,
    position: 'relative',
  },
  certificationLogo: {
    width: 180,
    height: 180,
    borderRadius: 20,
    marginBottom: 16,
  },
  certificationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    backgroundColor: '#F0FDF4',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
  certificationBadgeText: {
    fontSize: 12,
    color: '#059669',
    fontFamily: 'Inter-SemiBold',
    fontWeight: '600',
  },
  certificationInfo: {
    flex: 1,
  },
  certificationName: {
    fontSize: 16,
    color: '#111827',
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  certificationDescription: {
    fontSize: 13,
    color: '#6B7280',
    fontFamily: 'Inter-Regular',
    lineHeight: 18,
  },
  verifiedBadge: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0FDF4',
    justifyContent: 'center',
    alignItems: 'center',
  },
   ctaPrimaryButton: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 16,
    alignItems: 'center',
  },
  ctaPrimaryButtonText: {
    color: '#059669',
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
  },
  ctaSecondaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  ctaSecondaryButtonText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    marginLeft: 8,
  },
   contactInfo: {
    alignItems: 'center',
    gap: 12,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  contactText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    marginLeft: 8,
    opacity: 0.9,
  },


  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 4,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  logo: {
    width: 130,
    height: 48,
    borderRadius: 12,
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logoTextContainer: {
    flex: 1,
  },
  logoText: {
    fontFamily: 'Inter-Bold',
    fontSize: 18,
    color: '#10B981',
    letterSpacing: -0.5,
  },
  logoSubtext: {
    fontFamily: 'Inter-Medium',
    fontSize: 12,
    color: '#6B7280',
  },
  headerStats: {
    flexDirection: 'row',
    gap: 20,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: '#F0FDF4',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  statText: {
    fontFamily: 'Inter-Medium',
    fontSize: 11,
    color: '#059669',
    fontWeight: '600',
  },
  heroSection: {
    paddingHorizontal: 20,
    paddingVertical: 32,
  },
  heroContent: {
    alignItems: 'center',
  },
  heroTitle: {
    fontFamily: 'Inter-Bold',
    fontSize: 32,
    color: '#111827',
    marginBottom: 16,
    lineHeight: 40,
    textAlign: 'center',
    letterSpacing: -1,
  },
  heroSubtitle: {
    fontFamily: 'Inter-Regular',
    fontSize: 18,
    color: '#6B7280',
    lineHeight: 28,
    marginBottom: 24,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  heroFeatures: {
    flexDirection: 'row',
    gap: 24,
  },
  heroFeature: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
    marginBottom: 5,
    marginRight: 8,
  },
  heroFeatureText: {
    fontFamily: 'Inter-Medium',
    fontSize: 13,
    color: '#374151',
    fontWeight: '600',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  serviceCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    marginRight: 16,
    marginBottom: 16,
    width: 260,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  serviceImage: {
    width: '100%',
    height: 140,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  serviceContent: {
    padding: 20,
  },
  serviceTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 17,
    color: '#111827',
    marginBottom: 10,
    letterSpacing: -0.3,
  },
  serviceDescription: {
    fontFamily: 'Inter-Regular',
    fontSize: 15,
    color: '#6B7280',
    lineHeight: 22,
  },
  servicesScroll: {
    marginTop: 8,
  },
  productCard: {
    width: 220,
    height: 260,
    borderRadius: 20,
    marginRight: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 8,
    position: 'relative',
  },
  productImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  productOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '60%',
  },
  productInfo: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
  },
  
  
  productName: {
    fontSize: 17,
    color: '#FFFFFF',
    fontFamily: 'Inter-Bold',
    marginBottom: 4,
    letterSpacing: -0.4,
  },
  productBrand: {
    fontSize: 14,
    color: '#FFFFFF',
    fontFamily: 'Inter-Medium',
    opacity: 0.8,
    marginBottom: 12,
  },
  
  seeAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: '#F8FAFC',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  seeAllText: {
    fontSize: 13,
    color: '#667eea',
    fontFamily: 'Inter-SemiBold',
    fontWeight: '600',
  },
});